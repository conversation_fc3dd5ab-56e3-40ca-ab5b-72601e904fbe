using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace OutlineGenerator
{
    /// <summary>
    /// 外轮廓生成器核心类
    /// 基于Lee Mac的AutoLISP代码实现
    /// </summary>
    public class OutlineCore
    {
        /// <summary>
        /// 生成选中对象的外轮廓
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="database">数据库对象</param>
        /// <param name="editor">编辑器对象</param>
        /// <returns>生成的轮廓对象ID集合</returns>
        public static ObjectIdCollection GenerateOutline(SelectionSet selectionSet, Database database, Editor editor)
        {
            if (selectionSet == null || selectionSet.Count == 0)
                return new ObjectIdCollection();

            var result = new ObjectIdCollection();

            using (var transaction = database.TransactionManager.StartTransaction())
            {
                try
                {
                    // 计算选择集的边界框
                    var boundingBox = CalculateSelectionSetBoundingBox(selectionSet, transaction);
                    if (!boundingBox.HasValue)
                        return result;

                    // 创建临时边界框
                    var tempBoundary = CreateTemporaryBoundary(boundingBox.Value, database, transaction);
                    if (tempBoundary == ObjectId.Null)
                        return result;

                    // 执行边界命令生成轮廓
                    var outlineIds = ExecuteBoundaryCommand(selectionSet, tempBoundary, boundingBox.Value, editor, database, transaction);

                    // 删除临时边界框
                    var tempEntity = transaction.GetObject(tempBoundary, OpenMode.ForWrite) as Entity;
                    tempEntity?.Erase();

                    // 过滤掉与边界框面积相同的多段线
                    var filteredIds = FilterOutlineResults(outlineIds, boundingBox.Value, transaction);

                    foreach (ObjectId id in filteredIds)
                    {
                        result.Add(id);
                    }

                    transaction.Commit();
                }
                catch (System.Exception ex)
                {
                    transaction.Abort();
                    editor.WriteMessage($"\n错误: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 计算选择集的边界框
        /// </summary>
        private static Extents3d? CalculateSelectionSetBoundingBox(SelectionSet selectionSet, Transaction transaction)
        {
            Point3d? minPoint = null;
            Point3d? maxPoint = null;

            foreach (SelectedObject selectedObj in selectionSet)
            {
                if (selectedObj == null) continue;

                var entity = transaction.GetObject(selectedObj.ObjectId, OpenMode.ForRead) as Entity;
                if (entity == null) continue;

                try
                {
                    var bounds = entity.GeometricExtents;
                    
                    if (minPoint == null)
                    {
                        minPoint = bounds.MinPoint;
                        maxPoint = bounds.MaxPoint;
                    }
                    else
                    {
                        minPoint = new Point3d(
                            Math.Min(minPoint.Value.X, bounds.MinPoint.X),
                            Math.Min(minPoint.Value.Y, bounds.MinPoint.Y),
                            Math.Min(minPoint.Value.Z, bounds.MinPoint.Z)
                        );
                        maxPoint = new Point3d(
                            Math.Max(maxPoint.Value.X, bounds.MaxPoint.X),
                            Math.Max(maxPoint.Value.Y, bounds.MaxPoint.Y),
                            Math.Max(maxPoint.Value.Z, bounds.MaxPoint.Z)
                        );
                    }
                }
                catch
                {
                    // 忽略无法获取边界的对象
                    continue;
                }
            }

            if (minPoint == null || maxPoint == null)
                return null;

            return new Extents3d(minPoint.Value, maxPoint.Value);
        }

        /// <summary>
        /// 创建临时边界框
        /// </summary>
        private static ObjectId CreateTemporaryBoundary(Extents3d boundingBox, Database database, Transaction transaction)
        {
            try
            {
                var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
                var offset = distance * 1.5;

                var minPt = new Point2d(boundingBox.MinPoint.X - offset, boundingBox.MinPoint.Y - offset);
                var maxPt = new Point2d(boundingBox.MaxPoint.X + offset, boundingBox.MaxPoint.Y + offset);

                var polyline = new Polyline();
                polyline.AddVertexAt(0, new Point2d(minPt.X, minPt.Y), 0, 0, 0);
                polyline.AddVertexAt(1, new Point2d(maxPt.X, minPt.Y), 0, 0, 0);
                polyline.AddVertexAt(2, new Point2d(maxPt.X, maxPt.Y), 0, 0, 0);
                polyline.AddVertexAt(3, new Point2d(minPt.X, maxPt.Y), 0, 0, 0);
                polyline.Closed = true;

                var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                var objectId = modelSpace.AppendEntity(polyline);
                transaction.AddNewlyCreatedDBObject(polyline, true);

                return objectId;
            }
            catch
            {
                return ObjectId.Null;
            }
        }

        /// <summary>
        /// 执行边界命令生成轮廓
        /// </summary>
        private static ObjectIdCollection ExecuteBoundaryCommand(SelectionSet selectionSet, ObjectId tempBoundary,
            Extents3d boundingBox, Editor editor, Database database, Transaction transaction)
        {
            var result = new ObjectIdCollection();

            try
            {
                // 计算检测点（在边界框外但在临时边界内）
                var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
                var detectPoint = new Point3d(
                    boundingBox.MinPoint.X - distance / 3.0,
                    boundingBox.MinPoint.Y - distance / 3.0,
                    0
                );

                // 使用命令行方式执行边界检测
                var oldCmdEcho = Application.GetSystemVariable("CMDECHO");
                Application.SetSystemVariable("CMDECHO", 0);

                // 创建选择集字符串
                var selectionIds = new List<ObjectId>();
                foreach (SelectedObject selectedObj in selectionSet)
                {
                    selectionIds.Add(selectedObj.ObjectId);
                }
                selectionIds.Add(tempBoundary);

                // 记录当前最后一个对象
                var lastEnt = GetLastEntity(database, transaction);

                // 执行边界命令
                editor.Command("_.-boundary", "_a", "_b", "_n");

                // 选择对象
                foreach (var id in selectionIds)
                {
                    editor.Command(id);
                }

                editor.Command("", "_i", "_y", "_o", "_p", "", "_non", detectPoint, "");

                // 等待命令完成
                while ((int)Application.GetSystemVariable("CMDACTIVE") > 0)
                {
                    System.Threading.Thread.Sleep(100);
                }

                // 获取新创建的对象
                var newObjects = GetNewEntities(database, transaction, lastEnt);
                foreach (var id in newObjects)
                {
                    result.Add(id);
                }

                Application.SetSystemVariable("CMDECHO", oldCmdEcho);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n边界检测错误: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取数据库中的最后一个实体
        /// </summary>
        private static ObjectId GetLastEntity(Database database, Transaction transaction)
        {
            var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
            ObjectId lastId = ObjectId.Null;

            foreach (ObjectId id in modelSpace)
            {
                lastId = id;
            }

            return lastId;
        }

        /// <summary>
        /// 获取指定对象之后新创建的实体
        /// </summary>
        private static List<ObjectId> GetNewEntities(Database database, Transaction transaction, ObjectId afterThis)
        {
            var result = new List<ObjectId>();
            var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
            bool foundAfter = (afterThis == ObjectId.Null);

            foreach (ObjectId id in modelSpace)
            {
                if (foundAfter)
                {
                    result.Add(id);
                }
                else if (id == afterThis)
                {
                    foundAfter = true;
                }
            }

            return result;
        }

        /// <summary>
        /// 过滤轮廓结果，移除与边界框面积相同的多段线
        /// </summary>
        private static ObjectIdCollection FilterOutlineResults(ObjectIdCollection outlineIds, Extents3d boundingBox, Transaction transaction)
        {
            var result = new ObjectIdCollection();
            var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
            var offset = distance * 1.5;
            var boundaryArea = (boundingBox.MaxPoint.X - boundingBox.MinPoint.X + 2 * offset) * 
                              (boundingBox.MaxPoint.Y - boundingBox.MinPoint.Y + 2 * offset);

            foreach (ObjectId id in outlineIds)
            {
                try
                {
                    var entity = transaction.GetObject(id, OpenMode.ForRead);
                    
                    // 检查是否为多段线且面积是否与边界框相同
                    if (entity is Polyline polyline)
                    {
                        if (Math.Abs(polyline.Area - boundaryArea) > 1e-4)
                        {
                            result.Add(id);
                        }
                        else
                        {
                            // 删除与边界框面积相同的多段线
                            var entityForWrite = transaction.GetObject(id, OpenMode.ForWrite) as Entity;
                            entityForWrite?.Erase();
                        }
                    }
                    else if (entity is Region region)
                    {
                        if (Math.Abs(region.Area - boundaryArea) > 1e-4)
                        {
                            result.Add(id);
                        }
                        else
                        {
                            // 删除与边界框面积相同的区域
                            var entityForWrite = transaction.GetObject(id, OpenMode.ForWrite) as Entity;
                            entityForWrite?.Erase();
                        }
                    }
                    else
                    {
                        result.Add(id);
                    }
                }
                catch
                {
                    // 如果无法处理某个对象，仍然保留它
                    result.Add(id);
                }
            }

            return result;
        }
    }
}
