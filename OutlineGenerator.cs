using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace OutlineGenerator
{
    /// <summary>
    /// 外轮廓生成器核心类
    /// 基于Lee Mac的AutoLISP代码实现
    /// </summary>
    public class OutlineCore
    {
        /// <summary>
        /// 生成选中对象的外轮廓 - 完全按照原始AutoLISP逻辑实现
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="database">数据库对象</param>
        /// <param name="editor">编辑器对象</param>
        /// <returns>生成的轮廓对象ID集合</returns>
        public static ObjectIdCollection GenerateOutline(SelectionSet selectionSet, Database database, Editor editor)
        {
            if (selectionSet == null || selectionSet.Count == 0)
                return new ObjectIdCollection();

            var result = new ObjectIdCollection();

            try
            {
                // 1. 计算选择集的边界框 (对应 LM:ssboundingbox)
                var boundingBox = CalculateSelectionSetBoundingBox(selectionSet);
                if (!boundingBox.HasValue)
                    return result;

                var box = boundingBox.Value;

                // 2. 计算距离和扩展边界 (对应原始算法的 dis 和 lst 计算)
                var distance = box.MinPoint.DistanceTo(box.MaxPoint) / 20.0;

                // 调整距离 (对应 dis (* dis 1.5))
                distance *= 1.5;
                var finalExpandedMin = new Point3d(box.MinPoint.X - distance, box.MinPoint.Y - distance, 0);
                var finalExpandedMax = new Point3d(box.MaxPoint.X + distance, box.MaxPoint.Y + distance, 0);

                // 计算临时边界的面积 (对应 are 变量) - 使用最终的扩展边界
                var tempBoundaryArea = (finalExpandedMax.X - finalExpandedMin.X) * (finalExpandedMax.Y - finalExpandedMin.Y);

                using (var transaction = database.TransactionManager.StartTransaction())
                {
                    // 3. 创建临时边界多段线 (对应 entmakex)
                    var tempBoundary = CreateTemporaryBoundaryPolyline(finalExpandedMin, finalExpandedMax, database, transaction);
                    if (tempBoundary == ObjectId.Null)
                        return result;

                    // 4. 缩放视图 (对应 vlax-invoke zoomwindow)
                    ZoomToWindow(finalExpandedMin, new Point3d(finalExpandedMax.X, finalExpandedMax.Y, 0));

                    // 5. 执行边界命令 (对应 command boundary)
                    var outlineIds = ExecuteBoundaryCommandOriginal(selectionSet, tempBoundary, box, distance, editor);

                    // 6. 删除临时边界 (对应 entdel ent) - 必须在过滤之前删除
                    var tempEntity = transaction.GetObject(tempBoundary, OpenMode.ForWrite) as Entity;
                    tempEntity?.Erase();

                    // 7. 过滤结果，删除与临时边界面积相同的对象 (对应while循环过滤)
                    var outlineIdsList = new List<ObjectId>();
                    foreach (ObjectId id in outlineIds)
                    {
                        outlineIdsList.Add(id);
                    }
                    var filteredIds = FilterOutlineResultsOriginal(outlineIdsList, tempBoundaryArea, transaction);

                    foreach (ObjectId id in filteredIds)
                    {
                        result.Add(id);
                    }

                    // 8. 恢复视图 (对应 vla-zoomprevious)
                    ZoomPrevious();

                    transaction.Commit();
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n错误: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 计算选择集的边界框 - 对应原始 LM:ssboundingbox 函数
        /// </summary>
        private static Extents3d? CalculateSelectionSetBoundingBox(SelectionSet selectionSet)
        {
            var minPoints = new List<Point3d>();
            var maxPoints = new List<Point3d>();

            using (var transaction = Application.DocumentManager.MdiActiveDocument.Database.TransactionManager.StartTransaction())
            {
                foreach (SelectedObject selectedObj in selectionSet)
                {
                    if (selectedObj == null) continue;

                    try
                    {
                        var entity = transaction.GetObject(selectedObj.ObjectId, OpenMode.ForRead) as Entity;
                        if (entity == null) continue;

                        // 检查对象是否有 getboundingbox 方法可用
                        var bounds = entity.GeometricExtents;
                        minPoints.Add(bounds.MinPoint);
                        maxPoints.Add(bounds.MaxPoint);
                    }
                    catch
                    {
                        // 忽略无法获取边界的对象
                        continue;
                    }
                }
                transaction.Commit();
            }

            if (minPoints.Count == 0 || maxPoints.Count == 0)
                return null;

            // 计算最小和最大点
            var finalMinPoint = new Point3d(
                minPoints.Min(p => p.X),
                minPoints.Min(p => p.Y),
                minPoints.Min(p => p.Z)
            );

            var finalMaxPoint = new Point3d(
                maxPoints.Max(p => p.X),
                maxPoints.Max(p => p.Y),
                maxPoints.Max(p => p.Z)
            );

            return new Extents3d(finalMinPoint, finalMaxPoint);
        }

        /// <summary>
        /// 创建临时边界多段线 - 对应原始 entmakex 调用
        /// </summary>
        private static ObjectId CreateTemporaryBoundaryPolyline(Point3d minPoint, Point3d maxPoint, Database database, Transaction transaction)
        {
            try
            {
                var polyline = new Polyline();

                // 按照原始AutoLISP的顺序添加顶点
                // (caar cadar) = (minX, minY)
                polyline.AddVertexAt(0, new Point2d(minPoint.X, minPoint.Y), 0, 0, 0);
                // (caadr cadar) = (maxX, minY)
                polyline.AddVertexAt(1, new Point2d(maxPoint.X, minPoint.Y), 0, 0, 0);
                // (caadr cadadr) = (maxX, maxY)
                polyline.AddVertexAt(2, new Point2d(maxPoint.X, maxPoint.Y), 0, 0, 0);
                // (caar cadadr) = (minX, maxY)
                polyline.AddVertexAt(3, new Point2d(minPoint.X, maxPoint.Y), 0, 0, 0);

                polyline.Closed = true;

                var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                var objectId = modelSpace.AppendEntity(polyline);
                transaction.AddNewlyCreatedDBObject(polyline, true);

                return objectId;
            }
            catch
            {
                return ObjectId.Null;
            }
        }

        /// <summary>
        /// 缩放视图到指定窗口 - 对应 vlax-invoke zoomwindow
        /// </summary>
        private static void ZoomToWindow(Point3d point1, Point3d point2)
        {
            try
            {
                var editor = Application.DocumentManager.MdiActiveDocument.Editor;
                editor.Command("_ZOOM", "_W", point1, point2);
            }
            catch
            {
                // 忽略缩放错误
            }
        }

        /// <summary>
        /// 恢复上一个视图 - 对应 vla-zoomprevious
        /// </summary>
        private static void ZoomPrevious()
        {
            try
            {
                var editor = Application.DocumentManager.MdiActiveDocument.Editor;
                editor.Command("_ZOOM", "_P");
            }
            catch
            {
                // 忽略缩放错误
            }
        }

        /// <summary>
        /// 执行边界命令 - 完全按照原始AutoLISP逻辑
        /// </summary>
        private static ObjectIdCollection ExecuteBoundaryCommandOriginal(SelectionSet selectionSet, ObjectId tempBoundary,
            Extents3d boundingBox, double distance, Editor editor)
        {
            var result = new ObjectIdCollection();

            try
            {
                // 保存当前命令回显设置
                var oldCmdEcho = Application.GetSystemVariable("CMDECHO");

                // 获取当前最后一个实体
                var lastEntity = GetLastEntity();

                // 关闭命令回显
                Application.SetSystemVariable("CMDECHO", 0);

                // 计算检测点 - 对应 (trans (mapcar '- (car box) (list (/ dis 3.0) (/ dis 3.0))) 0 1)
                // 注意：这里的distance是原始计算的distance，不是*1.5后的
                var originalDistance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
                var detectPoint = new Point3d(
                    boundingBox.MinPoint.X - originalDistance / 3.0,
                    boundingBox.MinPoint.Y - originalDistance / 3.0,
                    0
                );

                // 执行边界命令 - 使用单个Command调用，更接近原始AutoLISP
                // 构建选择集字符串
                var selectionObjects = new List<object>();
                foreach (SelectedObject selectedObj in selectionSet)
                {
                    selectionObjects.Add(selectedObj.ObjectId);
                }
                selectionObjects.Add(tempBoundary);

                // 构建完整的命令参数列表
                var commandArgs = new List<object>
                {
                    "_.-boundary", "_a", "_b", "_n"
                };

                // 添加选择对象
                commandArgs.AddRange(selectionObjects);

                // 添加其余参数
                commandArgs.AddRange(new object[]
                {
                    "", "_i", "_y", "_o", "_p", "", "_non", detectPoint, ""
                });

                // 执行命令
                editor.Command(commandArgs.ToArray());

                // 等待命令完成
                while ((int)Application.GetSystemVariable("CMDACTIVE") > 0)
                {
                    System.Threading.Thread.Sleep(50);
                }

                // 获取新创建的对象
                var newEntities = GetEntitiesAfter(lastEntity);
                foreach (var entityId in newEntities)
                {
                    result.Add(entityId);
                }

                // 恢复命令回显设置
                Application.SetSystemVariable("CMDECHO", oldCmdEcho);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n边界命令执行错误: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取数据库中的最后一个实体
        /// </summary>
        private static ObjectId GetLastEntity()
        {
            var database = Application.DocumentManager.MdiActiveDocument.Database;
            ObjectId lastId = ObjectId.Null;

            using (var transaction = database.TransactionManager.StartTransaction())
            {
                var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                foreach (ObjectId id in modelSpace)
                {
                    lastId = id;
                }
                transaction.Commit();
            }

            return lastId;
        }

        /// <summary>
        /// 获取指定实体之后创建的所有实体
        /// </summary>
        private static List<ObjectId> GetEntitiesAfter(ObjectId afterThis)
        {
            var result = new List<ObjectId>();
            var database = Application.DocumentManager.MdiActiveDocument.Database;

            using (var transaction = database.TransactionManager.StartTransaction())
            {
                var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                bool foundAfter = (afterThis == ObjectId.Null);

                foreach (ObjectId id in modelSpace)
                {
                    if (foundAfter)
                    {
                        result.Add(id);
                    }
                    else if (id == afterThis)
                    {
                        foundAfter = true;
                    }
                }
                transaction.Commit();
            }

            return result;
        }







        /// <summary>
        /// 过滤轮廓结果 - 对应原始AutoLISP的过滤逻辑
        /// </summary>
        private static ObjectIdCollection FilterOutlineResultsOriginal(List<ObjectId> outlineIds, double tempBoundaryArea, Transaction transaction)
        {
            var result = new ObjectIdCollection();

            foreach (ObjectId id in outlineIds)
            {
                try
                {
                    var entity = transaction.GetObject(id, OpenMode.ForRead) as Entity;
                    if (entity == null) continue;

                    // 检查对象是否有面积属性
                    bool hasAreaProperty = false;
                    double entityArea = 0;

                    if (entity is Polyline polyline)
                    {
                        hasAreaProperty = true;
                        entityArea = polyline.Area;
                    }
                    else if (entity is Region region)
                    {
                        hasAreaProperty = true;
                        entityArea = region.Area;
                    }
                    else if (entity is Hatch hatch)
                    {
                        hasAreaProperty = true;
                        entityArea = hatch.Area;
                    }

                    // 对应原始逻辑：(and (vlax-property-available-p obj 'area) (equal (vla-get-area obj) are 1e-4))
                    // 使用相对误差而不是绝对误差进行比较
                    if (hasAreaProperty)
                    {
                        var areaDifference = Math.Abs(entityArea - tempBoundaryArea);
                        var relativeError = tempBoundaryArea > 0 ? areaDifference / tempBoundaryArea : areaDifference;

                        // 如果面积相等（在误差范围内），则删除该对象
                        if (relativeError <= 1e-4 || areaDifference <= 1e-6)
                        {
                            // 删除与临时边界面积相同的对象 (对应 entdel)
                            var entityForWrite = transaction.GetObject(id, OpenMode.ForWrite) as Entity;
                            entityForWrite?.Erase();
                        }
                        else
                        {
                            // 添加到结果集 (对应 ssadd enl rtn)
                            result.Add(id);
                        }
                    }
                    else
                    {
                        // 没有面积属性的对象直接添加到结果集
                        result.Add(id);
                    }
                }
                catch
                {
                    // 如果无法处理某个对象，仍然保留它
                    result.Add(id);
                }
            }

            return result;
        }
    }
}
