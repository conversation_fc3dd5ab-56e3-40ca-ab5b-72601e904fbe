using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using Autodesk.AutoCAD.Runtime;

namespace OutlineGenerator
{
    /// <summary>
    /// 外轮廓生成器核心类
    /// 基于Lee Mac的AutoLISP代码实现
    /// </summary>
    public class OutlineCore
    {
        /// <summary>
        /// 生成选中对象的外轮廓
        /// </summary>
        /// <param name="selectionSet">选择集</param>
        /// <param name="database">数据库对象</param>
        /// <param name="editor">编辑器对象</param>
        /// <returns>生成的轮廓对象ID集合</returns>
        public static ObjectIdCollection GenerateOutline(SelectionSet selectionSet, Database database, Editor editor)
        {
            if (selectionSet == null || selectionSet.Count == 0)
                return new ObjectIdCollection();

            var result = new ObjectIdCollection();

            using (var transaction = database.TransactionManager.StartTransaction())
            {
                try
                {
                    // 计算选择集的边界框
                    var boundingBox = CalculateSelectionSetBoundingBox(selectionSet, transaction);
                    if (!boundingBox.HasValue)
                        return result;

                    // 创建临时边界框
                    var tempBoundary = CreateTemporaryBoundary(boundingBox.Value, database, transaction);
                    if (tempBoundary == ObjectId.Null)
                        return result;

                    // 执行边界命令生成轮廓
                    var outlineIds = ExecuteBoundaryCommand(selectionSet, tempBoundary, boundingBox.Value, editor, database, transaction);

                    // 删除临时边界框
                    var tempEntity = transaction.GetObject(tempBoundary, OpenMode.ForWrite) as Entity;
                    tempEntity?.Erase();

                    // 过滤掉与边界框面积相同的多段线
                    var filteredIds = FilterOutlineResults(outlineIds, boundingBox.Value, transaction);

                    foreach (ObjectId id in filteredIds)
                    {
                        result.Add(id);
                    }

                    transaction.Commit();
                }
                catch (System.Exception ex)
                {
                    transaction.Abort();
                    editor.WriteMessage($"\n错误: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 计算选择集的边界框
        /// </summary>
        private static Extents3d? CalculateSelectionSetBoundingBox(SelectionSet selectionSet, Transaction transaction)
        {
            Point3d? minPoint = null;
            Point3d? maxPoint = null;

            foreach (SelectedObject selectedObj in selectionSet)
            {
                if (selectedObj == null) continue;

                var entity = transaction.GetObject(selectedObj.ObjectId, OpenMode.ForRead) as Entity;
                if (entity == null) continue;

                try
                {
                    var bounds = entity.GeometricExtents;
                    
                    if (minPoint == null)
                    {
                        minPoint = bounds.MinPoint;
                        maxPoint = bounds.MaxPoint;
                    }
                    else
                    {
                        minPoint = new Point3d(
                            Math.Min(minPoint.Value.X, bounds.MinPoint.X),
                            Math.Min(minPoint.Value.Y, bounds.MinPoint.Y),
                            Math.Min(minPoint.Value.Z, bounds.MinPoint.Z)
                        );
                        maxPoint = new Point3d(
                            Math.Max(maxPoint.Value.X, bounds.MaxPoint.X),
                            Math.Max(maxPoint.Value.Y, bounds.MaxPoint.Y),
                            Math.Max(maxPoint.Value.Z, bounds.MaxPoint.Z)
                        );
                    }
                }
                catch
                {
                    // 忽略无法获取边界的对象
                    continue;
                }
            }

            if (minPoint == null || maxPoint == null)
                return null;

            return new Extents3d(minPoint.Value, maxPoint.Value);
        }

        /// <summary>
        /// 创建临时边界框
        /// </summary>
        private static ObjectId CreateTemporaryBoundary(Extents3d boundingBox, Database database, Transaction transaction)
        {
            try
            {
                var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
                var offset = distance * 1.5;

                var minPt = new Point2d(boundingBox.MinPoint.X - offset, boundingBox.MinPoint.Y - offset);
                var maxPt = new Point2d(boundingBox.MaxPoint.X + offset, boundingBox.MaxPoint.Y + offset);

                var polyline = new Polyline();
                polyline.AddVertexAt(0, new Point2d(minPt.X, minPt.Y), 0, 0, 0);
                polyline.AddVertexAt(1, new Point2d(maxPt.X, minPt.Y), 0, 0, 0);
                polyline.AddVertexAt(2, new Point2d(maxPt.X, maxPt.Y), 0, 0, 0);
                polyline.AddVertexAt(3, new Point2d(minPt.X, maxPt.Y), 0, 0, 0);
                polyline.Closed = true;

                var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                var objectId = modelSpace.AppendEntity(polyline);
                transaction.AddNewlyCreatedDBObject(polyline, true);

                return objectId;
            }
            catch
            {
                return ObjectId.Null;
            }
        }

        /// <summary>
        /// 执行边界命令生成轮廓
        /// </summary>
        private static ObjectIdCollection ExecuteBoundaryCommand(SelectionSet selectionSet, ObjectId tempBoundary,
            Extents3d boundingBox, Editor editor, Database database, Transaction transaction)
        {
            var result = new ObjectIdCollection();

            try
            {
                // 计算检测点（在边界框外但在临时边界内）
                var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
                var detectPoint = new Point3d(
                    boundingBox.MinPoint.X - distance / 3.0,
                    boundingBox.MinPoint.Y - distance / 3.0,
                    0
                );

                // 创建边界对象集合
                var boundaryObjects = new ObjectIdCollection();
                foreach (SelectedObject selectedObj in selectionSet)
                {
                    boundaryObjects.Add(selectedObj.ObjectId);
                }
                boundaryObjects.Add(tempBoundary);

                // 直接使用备用方法创建边界
                result = ExecuteBoundaryCommandFallback(selectionSet, tempBoundary, detectPoint, editor, database, transaction);
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n边界检测错误: {ex.Message}");
                // 尝试备用方法
                var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
                var detectPoint = new Point3d(
                    boundingBox.MinPoint.X - distance / 3.0,
                    boundingBox.MinPoint.Y - distance / 3.0,
                    0
                );
                result = ExecuteBoundaryCommandFallback(selectionSet, tempBoundary, detectPoint, editor, database, transaction);
            }

            return result;
        }



        /// <summary>
        /// 备用边界检测方法 - 使用凸包算法
        /// </summary>
        private static ObjectIdCollection ExecuteBoundaryCommandFallback(SelectionSet selectionSet, ObjectId tempBoundary,
            Point3d detectPoint, Editor editor, Database database, Transaction transaction)
        {
            var result = new ObjectIdCollection();

            try
            {
                // 收集所有对象的关键点
                var allPoints = new List<Point2d>();

                foreach (SelectedObject selectedObj in selectionSet)
                {
                    var points = GetEntityKeyPoints(selectedObj.ObjectId, transaction);
                    allPoints.AddRange(points);
                }

                if (allPoints.Count > 0)
                {
                    // 计算凸包
                    var convexHull = CalculateConvexHull(allPoints);

                    if (convexHull.Count >= 3)
                    {
                        // 创建凸包多段线
                        var outlinePolyline = CreatePolylineFromPoints(convexHull);
                        if (outlinePolyline != null)
                        {
                            var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                            var outlineId = modelSpace.AppendEntity(outlinePolyline);
                            transaction.AddNewlyCreatedDBObject(outlinePolyline, true);
                            result.Add(outlineId);
                        }
                    }
                    else
                    {
                        // 如果凸包失败，使用边界框方法
                        var allObjects = new List<ObjectId>();
                        foreach (SelectedObject selectedObj in selectionSet)
                        {
                            allObjects.Add(selectedObj.ObjectId);
                        }

                        var totalBounds = CalculateTotalBounds(allObjects, transaction);
                        if (totalBounds.HasValue)
                        {
                            var outlinePolyline = CreateOutlinePolyline(totalBounds.Value);
                            if (outlinePolyline != null)
                            {
                                var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForWrite) as BlockTableRecord;
                                var outlineId = modelSpace.AppendEntity(outlinePolyline);
                                transaction.AddNewlyCreatedDBObject(outlinePolyline, true);
                                result.Add(outlineId);
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n备用边界检测错误: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 计算对象列表的总边界
        /// </summary>
        private static Extents3d? CalculateTotalBounds(List<ObjectId> objectIds, Transaction transaction)
        {
            Point3d? minPoint = null;
            Point3d? maxPoint = null;

            foreach (var id in objectIds)
            {
                try
                {
                    var entity = transaction.GetObject(id, OpenMode.ForRead) as Entity;
                    if (entity == null) continue;

                    var bounds = entity.GeometricExtents;

                    if (minPoint == null)
                    {
                        minPoint = bounds.MinPoint;
                        maxPoint = bounds.MaxPoint;
                    }
                    else
                    {
                        minPoint = new Point3d(
                            Math.Min(minPoint.Value.X, bounds.MinPoint.X),
                            Math.Min(minPoint.Value.Y, bounds.MinPoint.Y),
                            Math.Min(minPoint.Value.Z, bounds.MinPoint.Z)
                        );
                        maxPoint = new Point3d(
                            Math.Max(maxPoint.Value.X, bounds.MaxPoint.X),
                            Math.Max(maxPoint.Value.Y, bounds.MaxPoint.Y),
                            Math.Max(maxPoint.Value.Z, bounds.MaxPoint.Z)
                        );
                    }
                }
                catch
                {
                    continue;
                }
            }

            if (minPoint == null || maxPoint == null)
                return null;

            return new Extents3d(minPoint.Value, maxPoint.Value);
        }

        /// <summary>
        /// 获取实体的关键点
        /// </summary>
        private static List<Point2d> GetEntityKeyPoints(ObjectId entityId, Transaction transaction)
        {
            var points = new List<Point2d>();

            try
            {
                var entity = transaction.GetObject(entityId, OpenMode.ForRead) as Entity;
                if (entity == null) return points;

                // 获取边界框的角点
                var bounds = entity.GeometricExtents;
                points.Add(new Point2d(bounds.MinPoint.X, bounds.MinPoint.Y));
                points.Add(new Point2d(bounds.MaxPoint.X, bounds.MinPoint.Y));
                points.Add(new Point2d(bounds.MaxPoint.X, bounds.MaxPoint.Y));
                points.Add(new Point2d(bounds.MinPoint.X, bounds.MaxPoint.Y));

                // 根据实体类型获取更多关键点
                if (entity is Polyline polyline)
                {
                    for (int i = 0; i < polyline.NumberOfVertices; i++)
                    {
                        var pt = polyline.GetPoint2dAt(i);
                        points.Add(pt);
                    }
                }
                else if (entity is Line line)
                {
                    points.Add(new Point2d(line.StartPoint.X, line.StartPoint.Y));
                    points.Add(new Point2d(line.EndPoint.X, line.EndPoint.Y));
                }
                else if (entity is Circle circle)
                {
                    var center = new Point2d(circle.Center.X, circle.Center.Y);
                    var radius = circle.Radius;
                    points.Add(new Point2d(center.X - radius, center.Y));
                    points.Add(new Point2d(center.X + radius, center.Y));
                    points.Add(new Point2d(center.X, center.Y - radius));
                    points.Add(new Point2d(center.X, center.Y + radius));
                }
                else if (entity is Arc arc)
                {
                    points.Add(new Point2d(arc.StartPoint.X, arc.StartPoint.Y));
                    points.Add(new Point2d(arc.EndPoint.X, arc.EndPoint.Y));
                    points.Add(new Point2d(arc.Center.X, arc.Center.Y));
                }
            }
            catch
            {
                // 忽略错误，返回空列表
            }

            return points;
        }

        /// <summary>
        /// 计算点集的凸包（Graham扫描算法）
        /// </summary>
        private static List<Point2d> CalculateConvexHull(List<Point2d> points)
        {
            if (points.Count < 3) return points;

            // 找到最下方的点（Y最小，如果相同则X最小）
            var bottom = points.OrderBy(p => p.Y).ThenBy(p => p.X).First();

            // 按极角排序
            var sortedPoints = points.Where(p => p != bottom)
                .OrderBy(p => Math.Atan2(p.Y - bottom.Y, p.X - bottom.X))
                .ToList();

            var hull = new List<Point2d> { bottom };

            foreach (var point in sortedPoints)
            {
                // 移除不构成左转的点
                while (hull.Count > 1 && CrossProduct(hull[hull.Count - 2], hull[hull.Count - 1], point) <= 0)
                {
                    hull.RemoveAt(hull.Count - 1);
                }
                hull.Add(point);
            }

            return hull;
        }

        /// <summary>
        /// 计算叉积（用于判断转向）
        /// </summary>
        private static double CrossProduct(Point2d a, Point2d b, Point2d c)
        {
            return (b.X - a.X) * (c.Y - a.Y) - (b.Y - a.Y) * (c.X - a.X);
        }

        /// <summary>
        /// 从点列表创建多段线
        /// </summary>
        private static Polyline CreatePolylineFromPoints(List<Point2d> points)
        {
            try
            {
                var polyline = new Polyline();

                for (int i = 0; i < points.Count; i++)
                {
                    polyline.AddVertexAt(i, points[i], 0, 0, 0);
                }

                polyline.Closed = true;
                return polyline;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建外轮廓多段线
        /// </summary>
        private static Polyline CreateOutlinePolyline(Extents3d bounds)
        {
            try
            {
                var polyline = new Polyline();

                // 添加矩形顶点
                polyline.AddVertexAt(0, new Point2d(bounds.MinPoint.X, bounds.MinPoint.Y), 0, 0, 0);
                polyline.AddVertexAt(1, new Point2d(bounds.MaxPoint.X, bounds.MinPoint.Y), 0, 0, 0);
                polyline.AddVertexAt(2, new Point2d(bounds.MaxPoint.X, bounds.MaxPoint.Y), 0, 0, 0);
                polyline.AddVertexAt(3, new Point2d(bounds.MinPoint.X, bounds.MaxPoint.Y), 0, 0, 0);

                polyline.Closed = true;
                return polyline;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 过滤轮廓结果，移除与边界框面积相同的多段线
        /// </summary>
        private static ObjectIdCollection FilterOutlineResults(ObjectIdCollection outlineIds, Extents3d boundingBox, Transaction transaction)
        {
            var result = new ObjectIdCollection();
            var distance = boundingBox.MinPoint.DistanceTo(boundingBox.MaxPoint) / 20.0;
            var offset = distance * 1.5;
            var boundaryArea = (boundingBox.MaxPoint.X - boundingBox.MinPoint.X + 2 * offset) * 
                              (boundingBox.MaxPoint.Y - boundingBox.MinPoint.Y + 2 * offset);

            foreach (ObjectId id in outlineIds)
            {
                try
                {
                    var entity = transaction.GetObject(id, OpenMode.ForRead);
                    
                    // 检查是否为多段线且面积是否与边界框相同
                    if (entity is Polyline polyline)
                    {
                        if (Math.Abs(polyline.Area - boundaryArea) > 1e-4)
                        {
                            result.Add(id);
                        }
                        else
                        {
                            // 删除与边界框面积相同的多段线
                            var entityForWrite = transaction.GetObject(id, OpenMode.ForWrite) as Entity;
                            entityForWrite?.Erase();
                        }
                    }
                    else if (entity is Region region)
                    {
                        if (Math.Abs(region.Area - boundaryArea) > 1e-4)
                        {
                            result.Add(id);
                        }
                        else
                        {
                            // 删除与边界框面积相同的区域
                            var entityForWrite = transaction.GetObject(id, OpenMode.ForWrite) as Entity;
                            entityForWrite?.Erase();
                        }
                    }
                    else
                    {
                        result.Add(id);
                    }
                }
                catch
                {
                    // 如果无法处理某个对象，仍然保留它
                    result.Add(id);
                }
            }

            return result;
        }
    }
}
