using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Runtime;

namespace OutlineGenerator
{
    /// <summary>
    /// AutoCAD命令类
    /// 提供用户交互接口
    /// </summary>
    public class Commands
    {
        /// <summary>
        /// 外轮廓生成命令 - 对应原AutoLISP中的c:wlk命令
        /// </summary>
        [CommandMethod("WLK", CommandFlags.Modal)]
        public static void GenerateOutlineCommand()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            try
            {
                // 提示用户选择对象
                var selectionOptions = new PromptSelectionOptions
                {
                    MessageForAdding = "\n请选择要生成外轮廓的对象: ",
                    AllowDuplicates = false
                };

                var selectionResult = editor.GetSelection(selectionOptions);

                if (selectionResult.Status != PromptStatus.OK)
                {
                    editor.WriteMessage("\n命令已取消。");
                    return;
                }

                if (selectionResult.Value.Count == 0)
                {
                    editor.WriteMessage("\n未选择任何对象。");
                    return;
                }

                editor.WriteMessage($"\n已选择 {selectionResult.Value.Count} 个对象，正在生成外轮廓...");

                // 开始撤销标记
                using (var undoRecord = new UndoRecord(database))
                {
                    // 生成外轮廓
                    var outlineIds = OutlineCore.GenerateOutline(selectionResult.Value, database, editor);

                    if (outlineIds.Count > 0)
                    {
                        editor.WriteMessage($"\n成功生成 {outlineIds.Count} 个外轮廓对象。");
                        
                        // 选择生成的轮廓对象以便用户查看
                        var newSelection = SelectionSet.FromObjectIds(outlineIds);
                        editor.SetImpliedSelection(newSelection);
                    }
                    else
                    {
                        editor.WriteMessage("\n未能生成外轮廓，请检查选择的对象。");
                    }

                    undoRecord.Commit();
                }
            }
            catch (System.Exception ex)
            {
                editor.WriteMessage($"\n命令执行出错: {ex.Message}");
                
                // 记录详细错误信息到AutoCAD文本窗口
                if (ex.InnerException != null)
                {
                    editor.WriteMessage($"\n详细错误: {ex.InnerException.Message}");
                }
                
                // 可选：记录到日志文件
                LogError(ex);
            }
        }

        /// <summary>
        /// 外轮廓生成命令的中文别名
        /// </summary>
        [CommandMethod("外轮廓", CommandFlags.Modal)]
        public static void GenerateOutlineCommandChinese()
        {
            GenerateOutlineCommand();
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        [CommandMethod("WLKHELP", CommandFlags.Modal)]
        public static void ShowHelp()
        {
            var editor = Application.DocumentManager.MdiActiveDocument.Editor;
            
            editor.WriteMessage("\n=== 外轮廓生成器帮助 ===");
            editor.WriteMessage("\n命令: WLK 或 外轮廓");
            editor.WriteMessage("\n功能: 为选中的对象生成外轮廓多段线");
            editor.WriteMessage("\n");
            editor.WriteMessage("\n使用方法:");
            editor.WriteMessage("\n1. 输入命令 WLK 或 外轮廓");
            editor.WriteMessage("\n2. 选择要生成外轮廓的对象");
            editor.WriteMessage("\n3. 按回车确认选择");
            editor.WriteMessage("\n4. 程序将自动生成外轮廓");
            editor.WriteMessage("\n");
            editor.WriteMessage("\n注意事项:");
            editor.WriteMessage("\n- 确保选择的对象在当前视图范围内");
            editor.WriteMessage("\n- 复杂对象可能需要较长处理时间");
            editor.WriteMessage("\n- 生成的轮廓将添加到当前图层");
            editor.WriteMessage("\n");
            editor.WriteMessage("\n基于Lee Mac的AutoLISP代码的C#实现");
            editor.WriteMessage("\n版本: 1.0.0");
        }

        /// <summary>
        /// 记录错误信息到日志
        /// </summary>
        private static void LogError(Exception ex)
        {
            try
            {
                var logPath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    "OutlineGenerator_Error.log"
                );

                var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {ex.Message}\n{ex.StackTrace}\n\n";
                
                System.IO.File.AppendAllText(logPath, logEntry);
            }
            catch
            {
                // 忽略日志记录错误
            }
        }
    }

    /// <summary>
    /// 撤销记录辅助类
    /// </summary>
    public class UndoRecord : IDisposable
    {
        private readonly Database _database;
        private bool _committed = false;
        private bool _disposed = false;

        public UndoRecord(Database database)
        {
            _database = database;
            _database.TransactionManager.StartTransaction().Dispose();
            // 开始撤销标记
            try
            {
                Application.DocumentManager.MdiActiveDocument.Editor.Command("_UNDO", "_BEGIN");
            }
            catch
            {
                // 如果无法执行撤销命令，忽略错误
            }
        }

        public void Commit()
        {
            _committed = true;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    if (_committed)
                    {
                        Application.DocumentManager.MdiActiveDocument.Editor.Command("_UNDO", "_END");
                    }
                    else
                    {
                        Application.DocumentManager.MdiActiveDocument.Editor.Command("_UNDO", "_BACK");
                    }
                }
                catch
                {
                    // 忽略撤销命令错误
                }
                
                _disposed = true;
            }
        }
    }
}
