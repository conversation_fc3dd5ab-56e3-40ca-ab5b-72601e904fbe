using System;
using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using Autodesk.AutoCAD.EditorInput;
using Autodesk.AutoCAD.Geometry;
using OutlineGenerator;

namespace Examples
{
    /// <summary>
    /// 外轮廓生成器编程使用示例
    /// </summary>
    public class ProgrammaticUsageExamples
    {
        /// <summary>
        /// 示例1: 为指定的对象ID集合生成外轮廓
        /// </summary>
        public static void Example1_GenerateOutlineForSpecificObjects()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            using (var transaction = database.TransactionManager.StartTransaction())
            {
                try
                {
                    // 假设我们有一些对象ID
                    var objectIds = new ObjectIdCollection();
                    
                    // 这里应该添加实际的对象ID
                    // objectIds.Add(someObjectId);

                    // 创建选择集
                    var selectionSet = SelectionSet.FromObjectIds(objectIds);

                    // 生成外轮廓
                    var outlineIds = OutlineCore.GenerateOutline(selectionSet, database, editor);

                    editor.WriteMessage($"\n生成了 {outlineIds.Count} 个外轮廓对象");

                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    editor.WriteMessage($"\n错误: {ex.Message}");
                    transaction.Abort();
                }
            }
        }

        /// <summary>
        /// 示例2: 为当前图层的所有对象生成外轮廓
        /// </summary>
        public static void Example2_GenerateOutlineForCurrentLayer()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            using (var transaction = database.TransactionManager.StartTransaction())
            {
                try
                {
                    var modelSpace = transaction.GetObject(database.CurrentSpaceId, OpenMode.ForRead) as BlockTableRecord;
                    var objectIds = new ObjectIdCollection();

                    // 获取当前图层名称
                    var layerTable = transaction.GetObject(database.LayerTableId, OpenMode.ForRead) as LayerTable;
                    var currentLayerRecord = transaction.GetObject(database.Clayer, OpenMode.ForRead) as LayerTableRecord;
                    var currentLayerName = currentLayerRecord.Name;

                    // 遍历模型空间中的所有对象
                    foreach (ObjectId objectId in modelSpace)
                    {
                        var entity = transaction.GetObject(objectId, OpenMode.ForRead) as Entity;
                        if (entity != null && entity.Layer == currentLayerName)
                        {
                            objectIds.Add(objectId);
                        }
                    }

                    if (objectIds.Count > 0)
                    {
                        var selectionSet = SelectionSet.FromObjectIds(objectIds);
                        var outlineIds = OutlineCore.GenerateOutline(selectionSet, database, editor);
                        
                        editor.WriteMessage($"\n为当前图层 '{currentLayerName}' 的 {objectIds.Count} 个对象生成了 {outlineIds.Count} 个外轮廓");
                    }
                    else
                    {
                        editor.WriteMessage($"\n当前图层 '{currentLayerName}' 中没有找到对象");
                    }

                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    editor.WriteMessage($"\n错误: {ex.Message}");
                    transaction.Abort();
                }
            }
        }

        /// <summary>
        /// 示例3: 批量处理多个选择集
        /// </summary>
        public static void Example3_BatchProcessMultipleSelections()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            try
            {
                var totalOutlines = 0;
                var processedSets = 0;

                // 循环处理多个选择集
                while (true)
                {
                    var selectionOptions = new PromptSelectionOptions
                    {
                        MessageForAdding = $"\n选择第 {processedSets + 1} 组对象 (直接回车结束): "
                    };

                    var selectionResult = editor.GetSelection(selectionOptions);

                    if (selectionResult.Status != PromptStatus.OK)
                        break;

                    using (var undoRecord = new UndoRecord(database))
                    {
                        var outlineIds = OutlineCore.GenerateOutline(selectionResult.Value, database, editor);
                        totalOutlines += outlineIds.Count;
                        processedSets++;

                        editor.WriteMessage($"\n第 {processedSets} 组: 生成了 {outlineIds.Count} 个外轮廓");
                        undoRecord.Commit();
                    }
                }

                editor.WriteMessage($"\n批量处理完成: 共处理 {processedSets} 组对象，生成 {totalOutlines} 个外轮廓");
            }
            catch (Exception ex)
            {
                editor.WriteMessage($"\n批量处理错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 示例4: 自定义外轮廓生成参数
        /// </summary>
        public static void Example4_CustomOutlineGeneration()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            // 提示用户选择对象
            var selectionResult = editor.GetSelection("\n选择要生成外轮廓的对象: ");
            if (selectionResult.Status != PromptStatus.OK)
                return;

            // 提示用户输入偏移距离
            var distanceOptions = new PromptDistanceOptions("\n指定外轮廓偏移距离")
            {
                DefaultValue = 1.0,
                AllowNegative = false,
                AllowZero = false
            };

            var distanceResult = editor.GetDistance(distanceOptions);
            if (distanceResult.Status != PromptStatus.OK)
                return;

            // 这里可以扩展OutlineCore类来支持自定义参数
            // 目前的实现使用固定的算法参数
            using (var undoRecord = new UndoRecord(database))
            {
                var outlineIds = OutlineCore.GenerateOutline(selectionResult.Value, database, editor);
                
                if (outlineIds.Count > 0)
                {
                    editor.WriteMessage($"\n使用偏移距离 {distanceResult.Value:F2} 生成了 {outlineIds.Count} 个外轮廓");
                }

                undoRecord.Commit();
            }
        }

        /// <summary>
        /// 示例5: 带进度显示的外轮廓生成
        /// </summary>
        public static void Example5_OutlineGenerationWithProgress()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            var selectionResult = editor.GetSelection("\n选择要生成外轮廓的对象: ");
            if (selectionResult.Status != PromptStatus.OK)
                return;

            var objectCount = selectionResult.Value.Count;
            editor.WriteMessage($"\n开始处理 {objectCount} 个对象...");

            using (var progressMeter = new ProgressMeter())
            {
                progressMeter.Start("生成外轮廓");
                progressMeter.SetLimit(100);

                try
                {
                    progressMeter.MeterProgress();
                    editor.WriteMessage("\n正在计算边界框...");
                    
                    progressMeter.SetLimit(50);
                    progressMeter.MeterProgress();
                    
                    using (var undoRecord = new UndoRecord(database))
                    {
                        var outlineIds = OutlineCore.GenerateOutline(selectionResult.Value, database, editor);
                        
                        progressMeter.SetLimit(100);
                        progressMeter.MeterProgress();
                        
                        editor.WriteMessage($"\n完成! 生成了 {outlineIds.Count} 个外轮廓对象");
                        undoRecord.Commit();
                    }
                }
                finally
                {
                    progressMeter.Stop();
                }
            }
        }
    }

    /// <summary>
    /// 自定义命令示例
    /// </summary>
    public class CustomCommands
    {
        /// <summary>
        /// 快速外轮廓命令 - 使用默认设置
        /// </summary>
        [CommandMethod("QUICKOUTLINE")]
        public static void QuickOutlineCommand()
        {
            var document = Application.DocumentManager.MdiActiveDocument;
            var database = document.Database;
            var editor = document.Editor;

            // 自动选择所有可见对象
            var filter = new SelectionFilter(new TypedValue[] 
            {
                new TypedValue((int)DxfCode.Start, "LINE,CIRCLE,ARC,POLYLINE,LWPOLYLINE,SPLINE,ELLIPSE")
            });

            var selectionResult = editor.SelectAll(filter);
            
            if (selectionResult.Status == PromptStatus.OK && selectionResult.Value.Count > 0)
            {
                using (var undoRecord = new UndoRecord(database))
                {
                    var outlineIds = OutlineCore.GenerateOutline(selectionResult.Value, database, editor);
                    editor.WriteMessage($"\n快速生成: 为 {selectionResult.Value.Count} 个对象生成了 {outlineIds.Count} 个外轮廓");
                    undoRecord.Commit();
                }
            }
            else
            {
                editor.WriteMessage("\n未找到可生成外轮廓的对象");
            }
        }
    }
}
