# 外轮廓生成器 (OutlineGenerator)

基于Lee Mac的AutoLISP代码的C# .NET实现，用于在AutoCAD中生成选中对象的外轮廓。

## 功能特性

- 🎯 **智能轮廓生成**: 自动为选中的对象生成外轮廓多段线
- 🔧 **边界检测**: 使用AutoCAD内置的边界检测算法
- 📐 **精确计算**: 自动计算对象边界框并创建适当的检测区域
- 🚀 **高性能**: C# .NET实现，比AutoLISP版本更快
- 🌐 **多语言支持**: 支持中英文命令
- 📝 **详细日志**: 提供错误日志记录功能

## 安装要求

- AutoCAD 2020 或更高版本
- .NET Framework 4.8
- Windows 10/11

## 安装方法

1. 下载编译好的 `OutlineGenerator.dll` 文件
2. 将DLL文件复制到AutoCAD的插件目录或任意目录
3. 在AutoCAD中使用 `NETLOAD` 命令加载DLL文件
4. 或者将DLL路径添加到AutoCAD的启动套件中实现自动加载

## 使用方法

### 基本用法

1. 在AutoCAD命令行输入 `WLK` 或 `外轮廓`
2. 选择要生成外轮廓的对象
3. 按回车确认选择
4. 程序将自动生成外轮廓多段线

### 可用命令

- `WLK` - 主要的外轮廓生成命令
- `外轮廓` - 中文别名命令
- `WLKHELP` - 显示帮助信息

### 使用示例

```
命令: WLK
请选择要生成外轮廓的对象: [选择对象]
已选择 5 个对象，正在生成外轮廓...
成功生成 2 个外轮廓对象。
```

## 技术实现

### 核心算法

1. **边界框计算**: 计算所有选中对象的最小包围盒
2. **临时边界创建**: 创建比边界框稍大的临时矩形边界
3. **边界检测**: 使用AutoCAD的Boundary API进行轮廓检测
4. **结果过滤**: 移除与临时边界面积相同的多段线
5. **清理**: 删除临时对象并返回结果

### 类结构

- `OutlineCore`: 核心算法实现类
- `Commands`: AutoCAD命令接口类
- `UndoRecord`: 撤销操作辅助类

## 与原AutoLISP版本的对比

| 特性 | AutoLISP版本 | C# .NET版本 |
|------|-------------|-------------|
| 执行速度 | 较慢 | 更快 |
| 错误处理 | 基础 | 详细的异常处理和日志 |
| 代码维护 | 较难 | 面向对象，易维护 |
| 调试支持 | 有限 | 完整的Visual Studio调试支持 |
| 扩展性 | 有限 | 高度可扩展 |

## 开发环境设置

### 必需的引用

```xml
<Reference Include="AcCoreMgd" />
<Reference Include="AcDbMgd" />
<Reference Include="AcMgd" />
<Reference Include="AdWindows" />
```

### 编译配置

- 目标框架: .NET Framework 4.8
- 平台: x64
- 输出类型: 类库 (DLL)

## 故障排除

### 常见问题

1. **命令未找到**
   - 确保DLL已正确加载
   - 检查AutoCAD版本兼容性

2. **无法生成轮廓**
   - 确保选择的对象有有效的几何边界
   - 检查对象是否在当前视图范围内

3. **性能问题**
   - 避免选择过多复杂对象
   - 确保有足够的系统内存

### 日志文件

错误日志保存在: `%USERPROFILE%\Documents\OutlineGenerator_Error.log`

## 许可证

本项目基于Lee Mac的原始AutoLISP代码实现，遵循相应的使用条款。

## 贡献

欢迎提交问题报告和功能请求。

## 版本历史

- **v1.0.0** (2024-07-06)
  - 初始版本
  - 完整的AutoLISP功能移植
  - 添加中文支持
  - 改进的错误处理

## 致谢

感谢Lee Mac提供的原始AutoLISP代码和算法思路。

原始AutoLISP代码: [Lee Mac Programming](http://www.lee-mac.com)
