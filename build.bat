@echo off
echo ========================================
echo 外轮廓生成器 C# DLL 构建脚本
echo ========================================
echo.

REM 设置变量
set PROJECT_NAME=OutlineGenerator
set CONFIG=Release
set PLATFORM=x64

REM 检查是否安装了 .NET SDK
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 .NET SDK，请先安装 .NET SDK
    pause
    exit /b 1
)

echo 正在清理之前的构建...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo.
echo 正在还原 NuGet 包...
dotnet restore %PROJECT_NAME%.csproj
if %errorlevel% neq 0 (
    echo 错误: NuGet 包还原失败
    pause
    exit /b 1
)

echo.
echo 正在构建项目 (%CONFIG% - %PLATFORM%)...
dotnet build %PROJECT_NAME%.csproj -c %CONFIG% -p:Platform=%PLATFORM%
if %errorlevel% neq 0 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo.
echo 构建成功完成!
echo.
echo 输出文件位置:
echo - DLL文件: bin\%CONFIG%\%PROJECT_NAME%.dll
echo - PDB文件: bin\%CONFIG%\%PROJECT_NAME%.pdb
echo.

REM 检查输出文件是否存在
if exist "bin\%CONFIG%\%PROJECT_NAME%.dll" (
    echo ✓ DLL文件已生成
) else (
    echo ✗ DLL文件未找到
)

if exist "bin\%CONFIG%\%PROJECT_NAME%.pdb" (
    echo ✓ PDB文件已生成
) else (
    echo ✗ PDB文件未找到
)

echo.
echo 使用说明:
echo 1. 将 %PROJECT_NAME%.dll 复制到 AutoCAD 插件目录
echo 2. 在 AutoCAD 中使用 NETLOAD 命令加载 DLL
echo 3. 输入 WLK 或 外轮廓 命令开始使用
echo.

pause
