# 外轮廓生成器安装指南

## 系统要求

- **AutoCAD版本**: 2020 或更高版本
- **操作系统**: Windows 10/11 (64位)
- **.NET Framework**: 4.8 或更高版本
- **内存**: 建议 4GB 以上

## 编译安装

### 方法1: 使用构建脚本 (推荐)

1. 确保已安装 .NET SDK 6.0 或更高版本
2. 双击运行 `build.bat` 脚本
3. 等待编译完成
4. 在 `bin\Release\` 目录下找到生成的 `OutlineGenerator.dll`

### 方法2: 使用 Visual Studio

1. 打开 Visual Studio 2019/2022
2. 打开 `OutlineGenerator.csproj` 项目文件
3. 确保选择 "Release" 配置和 "x64" 平台
4. 点击 "生成" → "生成解决方案"
5. 在输出目录找到生成的 DLL 文件

### 方法3: 使用命令行

```bash
# 还原包
dotnet restore OutlineGenerator.csproj

# 编译项目
dotnet build OutlineGenerator.csproj -c Release -p:Platform=x64
```

## 在AutoCAD中安装

### 临时加载 (测试用)

1. 启动 AutoCAD
2. 在命令行输入 `NETLOAD`
3. 浏览并选择编译好的 `OutlineGenerator.dll` 文件
4. 点击 "打开" 完成加载
5. 输入 `WLK` 测试命令是否可用

### 永久安装 (推荐)

#### 方法1: 使用 AutoCAD 启动套件

1. 在 AutoCAD 中输入 `APPLOAD` 命令
2. 点击 "添加" 按钮
3. 浏览并选择 `OutlineGenerator.dll` 文件
4. 点击 "加载" 按钮
5. 在 "启动套件" 选项卡中点击 "添加"
6. 再次选择 DLL 文件，这样每次启动 AutoCAD 时都会自动加载

#### 方法2: 复制到 AutoCAD 插件目录

1. 将 `OutlineGenerator.dll` 复制到以下目录之一:
   ```
   C:\Program Files\Autodesk\AutoCAD 2024\Plug-ins\
   %APPDATA%\Autodesk\ApplicationPlugins\
   ```

2. 创建一个 `.bundle` 文件夹结构:
   ```
   OutlineGenerator.bundle\
   ├── Contents\
   │   └── OutlineGenerator.dll
   └── PackageContents.xml
   ```

3. 创建 `PackageContents.xml` 文件:
   ```xml
   <?xml version="1.0" encoding="utf-8"?>
   <ApplicationPackage 
       SchemaVersion="1.0" 
       AutodeskProduct="AutoCAD" 
       ProductType="Application" 
       Name="OutlineGenerator" 
       Description="外轮廓生成器" 
       Author="Your Name" 
       ProductCode="{12345678-1234-1234-1234-123456789012}" 
       UpgradeCode="{12345678-1234-1234-1234-123456789012}" 
       Version="1.0.0">
     <CompanyDetails Name="Your Company" />
     <Components>
       <RuntimeRequirements OS="Win64" Platform="AutoCAD*" SeriesMin="R24.0" SeriesMax="R25.0" />
       <ComponentEntry AppName="OutlineGenerator" ModuleName="./Contents/OutlineGenerator.dll" AppDescription="外轮廓生成器">
         <Commands GroupName="OutlineGenerator">
           <Command Global="WLK" Local="WLK" />
           <Command Global="外轮廓" Local="外轮廓" />
           <Command Global="WLKHELP" Local="WLKHELP" />
         </Commands>
       </ComponentEntry>
     </Components>
   </ApplicationPackage>
   ```

## 验证安装

1. 重启 AutoCAD
2. 在命令行输入 `WLK` 或 `外轮廓`
3. 如果出现选择提示，说明安装成功
4. 输入 `WLKHELP` 查看帮助信息

## 故障排除

### 常见问题

**问题1: 命令未找到**
- 解决方案: 确保 DLL 已正确加载，检查 AutoCAD 版本兼容性

**问题2: 加载时出现错误**
- 解决方案: 
  - 检查 .NET Framework 版本
  - 确保 AutoCAD .NET API 引用路径正确
  - 检查 DLL 是否为 64 位版本

**问题3: 功能异常**
- 解决方案:
  - 查看错误日志: `%USERPROFILE%\Documents\OutlineGenerator_Error.log`
  - 确保选择的对象有有效的几何边界
  - 检查当前图层设置

### 调试模式

如果需要调试，可以:

1. 使用 Debug 配置编译 DLL
2. 在 Visual Studio 中附加到 AutoCAD 进程
3. 设置断点进行调试

### 卸载

1. 在 AutoCAD 中输入 `NETUNLOAD`
2. 选择 `OutlineGenerator.dll`
3. 或者重启 AutoCAD

## 技术支持

如果遇到问题，请检查:

1. AutoCAD 版本兼容性
2. .NET Framework 版本
3. 系统架构 (必须是 64 位)
4. 错误日志文件

## 更新

要更新到新版本:

1. 卸载当前版本
2. 替换 DLL 文件
3. 重新加载或重启 AutoCAD

## 许可证

请遵循相应的软件许可协议使用本插件。
